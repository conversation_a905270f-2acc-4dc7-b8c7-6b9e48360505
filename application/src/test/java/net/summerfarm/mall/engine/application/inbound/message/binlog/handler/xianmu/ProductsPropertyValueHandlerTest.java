package net.summerfarm.mall.engine.application.inbound.message.binlog.handler.xianmu;

import com.alibaba.fastjson.JSON;
import net.summerfarm.mall.engine.application.inbound.message.binlog.event.DtsModelEvent;
import net.summerfarm.mall.engine.application.service.item.EsItemCommandService;
import net.summerfarm.mall.engine.common.enums.BinlogEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ProductsPropertyValueHandler 测试类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-07-09
 * @description 测试 products_property_value 表的 binlog 处理逻辑
 */
@ExtendWith(MockitoExtension.class)
class ProductsPropertyValueHandlerTest {

    @Mock
    private EsItemCommandService esProductsPropertyValueCommandServiceImpl;

    @InjectMocks
    private ProductsPropertyValueHandler handler;

    private DtsModelEvent dtsModelEvent;

    @BeforeEach
    void setUp() {
        dtsModelEvent = new DtsModelEvent();
    }

    @Test
    void testTableDmlName() {
        String tableName = handler.getTableDmlName();
        assert "products_property_value".equals(tableName);
    }

    @Test
    void testInsertOperation() throws Exception {
        // 准备测试数据 - 新增关键属性（sku为空）
        dtsModelEvent.setType(BinlogEnum.EventEnum.INSERT.name());
        List<Map<String, String>> data = new ArrayList<>();
        Map<String, String> insertData = new HashMap<>();
        insertData.put("pd_id", "12345");
        insertData.put("sku", null); // sku为空，应该更新keyProperty
        insertData.put("products_property_value", "苹果");
        insertData.put("products_property_id", "1");
        data.add(insertData);
        dtsModelEvent.setData(data);

        // 执行测试
        handler.tableDml(dtsModelEvent);

        // 验证调用
        verify(esProductsPropertyValueCommandServiceImpl, times(1)).saveItemInfo(any());
    }

    @Test
    void testUpdateOperationWithEmptySku() throws Exception {
        // 准备测试数据 - 更新关键属性（sku为空）
        dtsModelEvent.setType(BinlogEnum.EventEnum.UPDATE.name());
        
        List<Map<String, String>> data = new ArrayList<>();
        Map<String, String> updateData = new HashMap<>();
        updateData.put("pd_id", "12345");
        updateData.put("sku", null); // sku为空，应该更新keyProperty
        updateData.put("products_property_value", "新苹果品牌");
        updateData.put("products_property_id", "1");
        data.add(updateData);
        dtsModelEvent.setData(data);

        List<Map<String, String>> oldData = new ArrayList<>();
        Map<String, String> oldUpdateData = new HashMap<>();
        oldUpdateData.put("pd_id", "12345");
        oldUpdateData.put("sku", null);
        oldUpdateData.put("products_property_value", "旧苹果品牌");
        oldUpdateData.put("products_property_id", "1");
        oldData.add(oldUpdateData);
        dtsModelEvent.setOld(oldData);

        // 执行测试
        handler.tableDml(dtsModelEvent);

        // 验证调用
        verify(esProductsPropertyValueCommandServiceImpl, times(1)).saveItemInfo(any());
    }

    @Test
    void testUpdateOperationWithSku() throws Exception {
        // 准备测试数据 - 更新销售属性（sku不为空）
        dtsModelEvent.setType(BinlogEnum.EventEnum.UPDATE.name());
        
        List<Map<String, String>> data = new ArrayList<>();
        Map<String, String> updateData = new HashMap<>();
        updateData.put("pd_id", "12345");
        updateData.put("sku", "SKU001"); // sku不为空，应该更新PropertyValues
        updateData.put("products_property_value", "甜味");
        updateData.put("products_property_id", "2");
        data.add(updateData);
        dtsModelEvent.setData(data);

        List<Map<String, String>> oldData = new ArrayList<>();
        Map<String, String> oldUpdateData = new HashMap<>();
        oldUpdateData.put("pd_id", "12345");
        oldUpdateData.put("sku", "SKU001");
        oldUpdateData.put("products_property_value", "酸味");
        oldUpdateData.put("products_property_id", "2");
        oldData.add(oldUpdateData);
        dtsModelEvent.setOld(oldData);

        // 执行测试
        handler.tableDml(dtsModelEvent);

        // 验证调用
        verify(esProductsPropertyValueCommandServiceImpl, times(1)).saveItemInfo(any());
    }

    @Test
    void testDeleteOperation() throws Exception {
        // 准备测试数据 - 删除属性
        dtsModelEvent.setType(BinlogEnum.EventEnum.DELETE.name());
        List<Map<String, String>> data = new ArrayList<>();
        Map<String, String> deleteData = new HashMap<>();
        deleteData.put("pd_id", "12345");
        deleteData.put("sku", "SKU001");
        deleteData.put("products_property_value", "甜味");
        deleteData.put("products_property_id", "2");
        data.add(deleteData);
        dtsModelEvent.setData(data);

        // 执行测试
        handler.tableDml(dtsModelEvent);

        // 验证调用
        verify(esProductsPropertyValueCommandServiceImpl, times(1)).saveItemInfo(any());
    }

    @Test
    void testExceptionHandling() throws Exception {
        // 准备测试数据
        dtsModelEvent.setType(BinlogEnum.EventEnum.INSERT.name());
        List<Map<String, String>> data = new ArrayList<>();
        Map<String, String> insertData = new HashMap<>();
        insertData.put("pd_id", "12345");
        insertData.put("sku", null);
        insertData.put("products_property_value", "测试品牌");
        data.add(insertData);
        dtsModelEvent.setData(data);

        // 模拟异常
        doThrow(new RuntimeException("ES同步异常")).when(esProductsPropertyValueCommandServiceImpl).saveItemInfo(any());

        // 执行测试 - 应该不抛出异常
        handler.tableDml(dtsModelEvent);

        // 验证调用
        verify(esProductsPropertyValueCommandServiceImpl, times(1)).saveItemInfo(any());
    }
}
