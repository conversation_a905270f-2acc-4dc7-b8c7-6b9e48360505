package net.summerfarm.mall.engine.application.service.item.impl.es.xianmu;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.engine.application.service.item.EsItemCommandService;
import net.summerfarm.mall.engine.common.query.item.EsItemQueryInput;
import net.summerfarm.mall.engine.domain.item.MarketItemDomainService;
import net.summerfarm.mall.engine.domain.item.ProductsPropertyRepository;
import net.summerfarm.mall.engine.domain.item.entity.MarketItemEsInfoEntity;
import net.summerfarm.mall.engine.domain.item.entity.ProductsPropertyValueEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-07-20
 * @description
 */
@Slf4j
@Service("esProductsPropertyValueCommandServiceImpl")
public class EsProductsPropertyValueCommandServiceImpl implements EsItemCommandService {

    @Resource
    private MarketItemDomainService marketItemDomainService;
    @Resource
    private ProductsPropertyRepository productsPropertyRepository;
    @Override
    public void saveItemInfo(List<Map<String, String>> dtsList) throws Exception {
        if (CollectionUtils.isEmpty(dtsList)) {
            return;
        }
        log.info("esProductsPropertyValueCommandServiceImpl saveItemInfo param:{}", JSON.toJSONString(dtsList));
        for (Map<String, String> map : dtsList) {
            MarketItemEsInfoEntity esInfoEntity = new MarketItemEsInfoEntity();
            try {
                Long pdId = Long.valueOf(map.get("pd_id"));
                String sku = map.get("sku");

                // 处理关键属性 (当sku字段为空时)
                if (StringUtils.isEmpty(sku)) {
                    log.info("处理关键属性，pdId:{}", pdId);
                    List<ProductsPropertyValueEntity> keyPropertyValueList = productsPropertyRepository.selectKeyValueByPdId(pdId);
                    List<String> keyList = keyPropertyValueList.stream()
                            .filter(oo -> {
                                // 品牌单独处理
                                if (Objects.equals("品牌", oo.getName())) {
                                    esInfoEntity.setBrandName(oo.getProductsPropertyValue());
                                    return false;
                                }
                                return true;
                            })
                            .map(ProductsPropertyValueEntity::getProductsPropertyValue)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(keyList)) {
                        esInfoEntity.setKeyProperty(keyList);
                        log.info("设置关键属性，pdId:{}, keyProperty:{}", pdId, keyList);
                    }
                } else {
                    // 处理销售属性 (当sku字段不为空时)
                    log.info("处理销售属性，sku:{}", sku);
                    List<ProductsPropertyValueEntity> salePropertyList = productsPropertyRepository.selectSaleValueBySku(sku);
                    if (CollectionUtils.isNotEmpty(salePropertyList)) {
                        // 过滤出口味和级别属性
                        List<String> salePropertys = salePropertyList.stream()
                                .filter(vo -> Objects.equals("口味", vo.getName()) || Objects.equals("级别", vo.getName()))
                                .map(ProductsPropertyValueEntity::getProductsPropertyValue)
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(salePropertys)) {
                            esInfoEntity.setPropertyValues(salePropertys);
                            log.info("设置销售属性，sku:{}, propertyValues:{}", sku, salePropertys);
                        }
                    }
                }

                // 设置基本信息
                esInfoEntity.setOutId(pdId);
                esInfoEntity.setItemCode(sku);

                // 构建查询条件
                EsItemQueryInput input = new EsItemQueryInput();
                input.setOutId(esInfoEntity.getOutId());
                input.setItemCode(sku);

                // 更新ES信息
                marketItemDomainService.updateMarketItemEsInfoEntity(esInfoEntity, input);
                log.info("成功更新ES信息，pdId:{}, sku:{}", pdId, sku);
            } catch (Exception e) {
                log.error("EsProductsPropertyValueCommandServiceImpl saveItemInfoToES 异常,param:{}, esInfoEntity:{}",
                    JSON.toJSONString(map), JSON.toJSONString(esInfoEntity), e);
            }
        }
    }
}
