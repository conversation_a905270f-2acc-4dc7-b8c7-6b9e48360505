package net.summerfarm.mall.engine.application.service.item.impl.es;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.engine.application.service.item.EsItemCommonService;
import net.summerfarm.mall.engine.common.constants.ItemCenterConsts;
import net.summerfarm.mall.engine.common.enums.CommonStatus;
import net.summerfarm.mall.engine.common.enums.MarketItemEnum;
import net.summerfarm.mall.engine.common.query.item.InterestRateConfigQueryInput;
import net.summerfarm.mall.engine.common.query.item.InventoryQueryInput;
import net.summerfarm.mall.engine.domain.item.*;
import net.summerfarm.mall.engine.domain.item.entity.*;
import net.summerfarm.mall.engine.facade.goods.GoodsFacade;
import net.summerfarm.mall.engine.facade.goods.dto.ProductSkuDTO;
import net.summerfarm.mall.engine.facade.item.dto.MarketItemInfoDTO;
import net.summerfarm.mall.engine.facade.wms.SaleInventoryProviderFacade;
import net.summerfarm.mall.engine.facade.wms.dto.WarehouseSkuInventoryDetailDTO;
import net.summerfarm.mall.engine.facade.wnc.WarehouseSkuAreaNoQueryProviderFacade;
import net.summerfarm.mall.engine.facade.wnc.dto.WarehouseBySkuAreaNoDTO;
import net.summerfarm.wms.saleinventory.dto.req.QueryWarehouseSkuInventoryReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoDataReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoQueryReq;
import net.xianmu.common.exception.BizException;
import net.xianmu.inventory.client.saleinventory.dto.req.QueryWarehouseSkuInventoryReqDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-07-24
 * @description
 */
@Slf4j
@Service
public class EsItemCommonServiceImpl implements EsItemCommonService {

    @Resource
    private MarketItemQueryRepository marketItemQueryRepository;
    @Resource
    private InterestRateConfigQueryRepository interestRateConfigQueryRepository;
    @Resource
    private FrontCategoryQueryRepository frontCategoryQueryRepository;
    @Resource
    private ProductsPropertyRepository productsPropertyRepository;
    @Resource
    private WarehouseSkuAreaNoQueryProviderFacade warehouseSkuAreaNoQueryProviderFacade;
    @Resource
    private SaleInventoryProviderFacade saleInventoryProviderFacade;
    @Resource
    private GoodsFacade goodsFacade;
    @Resource
    private TimingRuleRepository timingRuleRepository;
    @Resource
    private AreaSkuRecordRepository areaSkuRecordRepository;

    private static final Long NEW_PRODUCT_DAYS = 30L;

    private static Pattern PD_NAME_PATTERN = Pattern.compile("\\(.*?\\)|（.*?）");

    @Override
    public void buildDocOfMarket(MarketItemEsInfoEntity esInfoEntity, MarketItemInfoDTO itemInfoDTO){
        if (MarketItemEnum.DeleteFlagEnum.DIRTY_DATA.getFlag().equals(itemInfoDTO.getDeleteFlag())){
            log.info("EsItemOnSaleCommandServiceImpl buildDocOfMarket 异常,param:{}", JSON.toJSONString(itemInfoDTO));
            throw new BizException("脏数据不处理");
        }
        log.info("EsItemOnSaleCommandServiceImpl buildDocOfMarket ,param:{}", JSON.toJSONString(itemInfoDTO));
        esInfoEntity.setMarketItemId(itemInfoDTO.getItemId());
        esInfoEntity.setMarketId(itemInfoDTO.getMarketId());
        esInfoEntity.setTitle(itemInfoDTO.getMarketTitle());
        //处理商品名称
        if (PD_NAME_PATTERN.matcher(itemInfoDTO.getMarketTitle()).find()) {
            Set<String> extSet = new HashSet<>();
            Matcher matcher = PD_NAME_PATTERN.matcher(itemInfoDTO.getMarketTitle());
            while (matcher.find()){
                String ext = matcher.group()
                        .replaceAll("\\(", "").replaceAll("\\(", "")
                        .replaceAll("（", "").replaceAll("）", "");
                extSet.add(ext);
            }
            esInfoEntity.setTitleExt(JSON.toJSONString(extSet));
            String pure = PD_NAME_PATTERN.matcher(itemInfoDTO.getMarketTitle()).replaceAll("");
            esInfoEntity.setTitlePure(pure);
        } else {
            esInfoEntity.setTitlePure(itemInfoDTO.getMarketTitle());
            esInfoEntity.setTitleExt("");
        }
        esInfoEntity.setSubTitle(itemInfoDTO.getMarketSubTitle());
        esInfoEntity.setCategoryId(itemInfoDTO.getCategoryId());
        esInfoEntity.setDeleteFlag(Integer.valueOf(itemInfoDTO.getMarketDeleteFlag()));
        esInfoEntity.setMainPicture(itemInfoDTO.getMarketMainPicture());
        esInfoEntity.setOutId(itemInfoDTO.getMarketOutId());
        esInfoEntity.setSlogan(itemInfoDTO.getSlogan());
        //marketItem
        esInfoEntity.setMarketItemId(itemInfoDTO.getItemId());
        esInfoEntity.setSpecification(itemInfoDTO.getSpecification());
        esInfoEntity.setSpecificationUnit(itemInfoDTO.getSpecificationUnit());
        esInfoEntity.setItemCode(itemInfoDTO.getItemCode());
        esInfoEntity.setMarketItemTitle(itemInfoDTO.getItemTitle());
        esInfoEntity.setMarketItemMainPicture(itemInfoDTO.getMainPicture());
        esInfoEntity.setMiniOrderQuantity(itemInfoDTO.getMiniOrderQuantity());
        esInfoEntity.setMarketItemDeleteFlag(itemInfoDTO.getDeleteFlag());
        esInfoEntity.setExtType(itemInfoDTO.getExtType());
        esInfoEntity.setBaseSaleUnit(itemInfoDTO.getBaseSaleUnit());
        esInfoEntity.setVideoUrl (itemInfoDTO.getVideoUrl());
        esInfoEntity.setAfterSaleRuleDetail(itemInfoDTO.getAfterSaleRuleDetail());
        esInfoEntity.setVideoUploadUser(itemInfoDTO.getVideoUploadUser ());
//        esInfoEntity.setVideoUploadTime(itemInfoDTO.getVideoUploadTime ());
        esInfoEntity.setQuoteType(itemInfoDTO.getQuoteType ());
        //处理前台类目数据
        List<FrontCategoryEntity> esList = frontCategoryQueryRepository.selectFrontCategoryByCategoryId(itemInfoDTO.getCategoryId().intValue());
        if (!org.springframework.util.CollectionUtils.isEmpty(esList)) {
            List<Map<String, String>> voList = esList.stream().map(et -> {
                Map<String, String> map = new HashMap<>(2);
                map.put("name", et.getName());
                map.put("parentName", et.getParentName());
                return map;
            }).collect(Collectors.toList());
            esInfoEntity.setCategory(voList);
        }
        //处理关键属性
        1
        List<ProductsPropertyValueEntity> keyPropertyValueList = productsPropertyRepository.selectKeyValueByPdId(itemInfoDTO.getMarketOutId());
        List<String> keyList = keyPropertyValueList.stream()
                .filter(oo -> {
                    if (Objects.equals("品牌", oo.getName())) {
                        esInfoEntity.setBrandName(oo.getProductsPropertyValue());
                        return false;
                    }
                    return true;
                })
                .map(ProductsPropertyValueEntity::getProductsPropertyValue).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(keyList)) {
            esInfoEntity.setKeyProperty(keyList);
        }

        //处理销售属性
        List<ProductsPropertyValueEntity> salePropertyList = productsPropertyRepository.selectSaleValueBySku(itemInfoDTO.getItemCode());
        if (CollectionUtils.isNotEmpty(salePropertyList)) {
            List<String> salePropertys = salePropertyList.stream ().filter (vo -> Objects.equals ("口味", vo.getName ()) || Objects.equals ("级别", vo.getName ())).map (ProductsPropertyValueEntity::getProductsPropertyValue).collect (Collectors.toList ());
            if (CollectionUtils.isNotEmpty(salePropertys)) {
                esInfoEntity.setPropertyValues (salePropertys);
            }
        }
        // 处理 sku 属性
        InventoryQueryInput input = new InventoryQueryInput();
        input.setSku(Collections.singletonList(itemInfoDTO.getItemCode()));
        List<InventoryEntity> inventory = marketItemQueryRepository.queryNeedSyncSku(input);
        if (CollectionUtil.isNotEmpty(inventory)){
            InventoryEntity inventoryEntity = inventory.get(0);
            esInfoEntity.setType(inventoryEntity.getType());
            esInfoEntity.setSubType(inventoryEntity.getSubType());
        }

    }

    @Override
    public void buildDocOfInterestRateConfig(Integer areaNo, String sku, MarketItemEsInfoEntity esInfoEntity) {
        log.info("EsItemOnSaleCommandServiceImpl buildDocOfInterestRateConfig ,areaNo:{}, sku:{}, param:{}", areaNo, sku, JSON.toJSONString(esInfoEntity));
        InterestRateConfigQueryInput input = new InterestRateConfigQueryInput();
        input.setSku(sku);
        input.setAreaNo(areaNo);
        // 查询毛利率信息
        InterestRateConfigEntity interestRateConfig = interestRateConfigQueryRepository.selectByAreaNoAndSku(input);
        if (interestRateConfig == null || interestRateConfig.getInterestRate() == null) {
            log.warn("获取毛利率信息异常,param:{}", input);
            return;
        }
        esInfoEntity.setInterestRate(interestRateConfig.getInterestRate());
    }

    @Override
    public void buildDocOfAreaStore(Integer areaNo, String sku, MarketItemEsInfoEntity esInfoEntity) {
        WarehouseBySkuAreaNoQueryReq req = new WarehouseBySkuAreaNoQueryReq();
        List<WarehouseBySkuAreaNoDataReq> areaSkuList = Lists.newArrayList();
        WarehouseBySkuAreaNoDataReq skuAreaNoDataReq = new WarehouseBySkuAreaNoDataReq();
        skuAreaNoDataReq.setSku(sku);
        skuAreaNoDataReq.setAreaNoList(Lists.newArrayList(areaNo));
        areaSkuList.add(skuAreaNoDataReq);
        req.setAreaSkuList(areaSkuList);
        List<WarehouseBySkuAreaNoDTO> warehouseBySkuAreaNoDTOS = warehouseSkuAreaNoQueryProviderFacade.queryBySkuAreNo(req);
        if (CollectionUtils.isEmpty(warehouseBySkuAreaNoDTOS)){
            log.error("无有效库存仓,sku：{},areaNO:{}",sku,areaNo);
            return;
        }
        WarehouseBySkuAreaNoDTO skuAreaNoDTO = warehouseBySkuAreaNoDTOS.get(0);
        Integer warehouseNo = skuAreaNoDTO.getWarehouseNo();
        esInfoEntity.setWarehouseNo(warehouseNo);
        QueryWarehouseSkuInventoryReqDTO reqDTO = new QueryWarehouseSkuInventoryReqDTO();
        reqDTO.setTenantId(ItemCenterConsts.XIANMU_TENANT_ID);
        reqDTO.setWarehouseNo (Long.valueOf (warehouseNo));
        reqDTO.setSkuCodeList (Collections.singletonList (sku));

        List<WarehouseSkuInventoryDetailDTO> warehouseSkuDTOS = saleInventoryProviderFacade.queryWarehouseSkuInventory(reqDTO);
        if (CollectionUtils.isEmpty(warehouseSkuDTOS)){
            log.error("无有效库存数据,sku：{},areaNO:{},warehouseNo:{}",sku,areaNo,warehouseNo);
            return;
        }
        WarehouseSkuInventoryDetailDTO warehouseSkuDetailDTO = warehouseSkuDTOS.get(0);

        Integer coreQuantity = WarehouseUtil.calcQuantityForCore(warehouseSkuDetailDTO);
        esInfoEntity.setCoreSaleOut(coreQuantity <= 0 ? 0 : 1);
        esInfoEntity.setCoreStoreQuantity(coreQuantity);

        Integer storeQuantity = WarehouseUtil.calcQuantityForNormal(warehouseSkuDetailDTO);
        esInfoEntity.setSaleOut(storeQuantity <= 0 ? 0 : 1);
        esInfoEntity.setStoreQuantity(storeQuantity);
    }
    @Override
    public void buildDocOfProducts(Long pdId, MarketItemEsInfoEntity esInfoEntity){
        ProductsEntity entity = marketItemQueryRepository.getProductsByPdId(pdId);
        esInfoEntity.setQualityTime(entity.getQualityTime());
        esInfoEntity.setQualityTimeUnit(entity.getQualityTimeUnit());
    }

    @Override
    public void buildDocOfProductSku(MarketItemEsInfoEntity esInfoEntity,String sku) {
        ProductSkuDTO productSkuDTO = goodsFacade.selectProductSkuDetailBySkuCode (sku);
        if(productSkuDTO != null){
            esInfoEntity.setBuyerId(productSkuDTO.getBuyerId ());
            esInfoEntity.setBuyerName(productSkuDTO.getBuyerName ());
            esInfoEntity.setNetWeightNum(productSkuDTO.getNetWeightNum ());
            esInfoEntity.setNetWeightUnit(productSkuDTO.getNetWeightUnit ());
            esInfoEntity.setWeightNum(productSkuDTO.getWeight ());
        }
    }

    @Override
    public void buildDocOfMarketItemLabel(MarketItemEsInfoEntity esInfoEntity) {
        Map<String, LocalDateTime> onSaleBySkuMap = areaSkuRecordRepository.listFirstOnSaleBySkus(esInfoEntity.getTargetId(), Lists.newArrayList(esInfoEntity.getItemCode()));
        LocalDateTime firstOnSaleTime = onSaleBySkuMap.getOrDefault(esInfoEntity.getItemCode(), null);
        if (firstOnSaleTime != null && firstOnSaleTime.plusDays(NEW_PRODUCT_DAYS).isAfter(LocalDateTime.now())) {
            esInfoEntity.setLabel(Lists.newArrayList("新品"));
        } else {
            esInfoEntity.setLabel(Lists.newArrayList());
        }
    }

    @Override
    public void buildDocOfTimingRule(MarketItemEsInfoEntity esInfoEntity) {
        TimingRuleEntity timingRuleEntity = timingRuleRepository.getInfoBySkuAndAreaNo(esInfoEntity.getItemCode(), esInfoEntity.getTargetId());
        if (timingRuleEntity != null) {
            esInfoEntity.setTiming(timingRuleEntity.getDisplay());
        } else {
            esInfoEntity.setTiming(CommonStatus.NO.getCode());
        }
    }
}
