package net.summerfarm.mall.engine.application.service.item.impl.es.xianmu;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.engine.application.service.item.EsItemCommandService;
import net.summerfarm.mall.engine.application.service.item.impl.es.WarehouseUtil;
import net.summerfarm.mall.engine.common.constants.ItemCenterConsts;
import net.summerfarm.mall.engine.common.query.item.EsItemQueryInput;
import net.summerfarm.mall.engine.common.query.item.InventoryQueryInput;
import net.summerfarm.mall.engine.domain.item.MarketItemDomainService;
import net.summerfarm.mall.engine.domain.item.MarketItemQueryRepository;
import net.summerfarm.mall.engine.domain.item.entity.InventoryEntity;
import net.summerfarm.mall.engine.domain.item.entity.MarketItemEsInfoEntity;
import net.summerfarm.mall.engine.facade.wms.SaleInventoryProviderFacade;
import net.summerfarm.mall.engine.facade.wms.dto.WarehouseSkuInventoryDetailDTO;
import net.summerfarm.mall.engine.facade.wnc.WarehouseSkuAreaNoQueryProviderFacade;
import net.summerfarm.wms.saleinventory.dto.req.QueryWarehouseSkuInventoryReq;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-07-20
 * @description
 */
@Slf4j
@Service("esWarehouseInventoryMappingCommandServiceImpl")
public class EsWarehouseInventoryMappingCommandServiceImpl implements EsItemCommandService {

    @Resource
    private MarketItemDomainService marketItemDomainService;
    @Resource
    private SaleInventoryProviderFacade saleInventoryProviderFacade;
    @Resource
    private MarketItemQueryRepository marketItemQueryRepository;
    @Resource
    private WarehouseSkuAreaNoQueryProviderFacade warehouseSkuAreaNoQueryProviderFacade;
    @Override
    public void saveItemInfo(List<Map<String, String>> dtsList) throws Exception {
        if (CollectionUtils.isEmpty(dtsList)) {
            return;
        }
        log.info("esWarehouseInventoryMappingCommandServiceImpl saveItemInfo param:{}",JSON.toJSONString(dtsList));
        for (Map<String, String> map : dtsList) {
            MarketItemEsInfoEntity esInfoEntity = new MarketItemEsInfoEntity();
            try {
                Integer warehouseNoOld = Integer.valueOf(map.get("warehouseNoOld"));
                Integer warehouseNo = Integer.valueOf(map.get("warehouse_no"));
                Integer storeNo = Integer.valueOf(map.get("store_no"));
                String sku = map.get("sku");
                InventoryQueryInput skuInput = new InventoryQueryInput();
                skuInput.setSku(Lists.newArrayList(sku));
                List<InventoryEntity> skuList = marketItemQueryRepository.queryNeedSyncSku(skuInput);
                if (CollectionUtils.isEmpty(skuList)){
                    log.warn("sku不存在, sku:{}", sku);
                    continue;
                }
                QueryWarehouseSkuInventoryReq reqDTO = new QueryWarehouseSkuInventoryReq();
                reqDTO.setTenantId(ItemCenterConsts.XIANMU_TENANT_ID);
                reqDTO.setWarehouseNoList(Lists.newArrayList(warehouseNo));
                reqDTO.setSkuCode(sku);
                List<WarehouseSkuInventoryDetailDTO> warehouseSkuDTOS = saleInventoryProviderFacade.queryWarehouseSkuInventory(reqDTO);
                if (CollectionUtils.isEmpty(warehouseSkuDTOS)){
                    log.warn("sku库存仓映射关系不存在, param:{}", reqDTO);
                    return;
                }
                //获取城配仓对应的运营城市
                List<Integer> areaNos = warehouseSkuAreaNoQueryProviderFacade.queryFenceListWithArea(storeNo);
                if (CollectionUtils.isEmpty(areaNos)) {
                    log.warn("城配仓无对应的运营城市, param:{}", reqDTO);
                    return;
                }
                WarehouseSkuInventoryDetailDTO warehouseSkuDetailDTO = warehouseSkuDTOS.get(0);

                Integer coreQuantity = WarehouseUtil.calcQuantityForCore(warehouseSkuDetailDTO);
                esInfoEntity.setCoreSaleOut(coreQuantity <= 0 ? 0 : 1);
                esInfoEntity.setCoreStoreQuantity(coreQuantity);

                Integer storeQuantity = WarehouseUtil.calcQuantityForNormal(warehouseSkuDetailDTO);
                esInfoEntity.setSaleOut(storeQuantity <= 0 ? 0 : 1);
                esInfoEntity.setStoreQuantity(storeQuantity);
                esInfoEntity.setWarehouseNo(warehouseNo);
                EsItemQueryInput input = new EsItemQueryInput();
                input.setTargetIds(areaNos);
                input.setItemCode(sku);
                input.setWarehouseNo(warehouseNoOld);
                marketItemDomainService.updateMarketItemEsInfoEntity(esInfoEntity,input);
            } catch (Exception e) {
                log.error("EsAreaStoreCommandServiceImpl saveItemInfoToES 异常,param:{},e:{}",JSON.toJSONString(esInfoEntity),e);
            }
        }
    }
    /**
     * 计算针对普通客户的库存数量
     * @param warehouseSkuDetail 仓库库存数量信息
     * @return 普通客户可购买的库存量
     */
    public static Integer calcQuantityForNormal(WarehouseSkuInventoryDetailDTO warehouseSkuDetail){
        Integer reserveMaxQuantity = warehouseSkuDetail.getReserveMaxQuantity();
        if(reserveMaxQuantity == null){
            return calcQuantityForCore(warehouseSkuDetail);
        }
        Integer reserveMinQuantity = warehouseSkuDetail.getReserveMinQuantity();
        Integer reserveUseQuantity = warehouseSkuDetail.getReserveUseQuantity();
        Integer onlineQuantity = warehouseSkuDetail.getOnlineQuantity();
        Integer quantity;
        if(reserveMaxQuantity > reserveUseQuantity + reserveMinQuantity){
            quantity = onlineQuantity + reserveUseQuantity > reserveMaxQuantity ? onlineQuantity - reserveMaxQuantity - reserveUseQuantity : 0;
        } else {
            quantity = onlineQuantity > reserveMinQuantity ? onlineQuantity - reserveMinQuantity: 0;
        }
        return quantity;
    }

    /**
     * 计算针对核心客户的库存数量
     * @param warehouseSkuDetail 仓库库存数量信息
     * @return 核心客户可购买的库存量
     */
    public static Integer calcQuantityForCore(WarehouseSkuInventoryDetailDTO warehouseSkuDetail){
        return warehouseSkuDetail.getOnlineQuantity();
    }
}
