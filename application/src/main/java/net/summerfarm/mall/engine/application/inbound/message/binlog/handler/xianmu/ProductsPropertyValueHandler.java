package net.summerfarm.mall.engine.application.inbound.message.binlog.handler.xianmu;


import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.engine.application.inbound.message.binlog.event.DtsModelEvent;
import net.summerfarm.mall.engine.application.inbound.message.binlog.handler.DbTableDmlStrategy;
import net.summerfarm.mall.engine.application.inbound.message.binlog.handler.DtsModelHandler;
import net.summerfarm.mall.engine.application.service.item.EsItemCommandService;
import net.summerfarm.mall.engine.common.enums.BinlogEnum;
import net.summerfarm.mall.engine.common.util.EsProductInfoUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-07-13
 * @description
 */
@Slf4j
@Component
public class ProductsPropertyValueHandler implements DbTableDmlStrategy {

    @Resource(name = "esProductsPropertyValueCommandServiceImpl")
    private EsItemCommandService esProductsPropertyValueCommandServiceImpl;

    @Override
    public String getTableDmlName() {
        return EsProductInfoUtil.PRODUCTS_PROPERTY_VALUE;
    }
    @Override
    public void tableDml(DtsModelEvent dtsModelEvent) {
        List<Map<String, String>> esInfos = new ArrayList<>();
        if (Objects.equals(dtsModelEvent.getType(), BinlogEnum.EventEnum.INSERT.name())) {
            // 新增处理：也需要同步到ES
            log.info("处理PRODUCTS_PROPERTY_VALUE表新增数据");
            dtsModelEvent.consumerData(map -> {
                esInfos.add(map);
            });
        } else if (Objects.equals(dtsModelEvent.getType(), BinlogEnum.EventEnum.UPDATE.name())) {
            log.info("处理PRODUCTS_PROPERTY_VALUE表更新数据");
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData(dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> dataMap = pair.getKey();
                Map<String, String> dataMapOld = pair.getValue();
                Boolean bool = EsProductInfoUtil.binlogToEsData(getTableDmlName(), dataMapOld);
                if (bool) {
                    esInfos.add(dataMap);
                    log.debug("检测到需要同步的字段变更，pdId:{}, sku:{}",
                        dataMap.get("pd_id"), dataMap.get("sku"));
                }
            }
        } else if (Objects.equals(dtsModelEvent.getType(), BinlogEnum.EventEnum.DELETE.name())) {
            // 删除处理：需要更新ES中相关商品的属性信息
            log.info("处理PRODUCTS_PROPERTY_VALUE表删除数据");
            dtsModelEvent.consumerData(map -> {
                esInfos.add(map);
            });
        }

        log.info("es同步PRODUCTS_PROPERTY_VALUE表数据，操作类型:{}, 同步变更数据:{}",
            dtsModelEvent.getType(), JSON.toJSONString(esInfos));
        if (CollectionUtils.isNotEmpty(esInfos)) {
            try {
                esProductsPropertyValueCommandServiceImpl.saveItemInfo(esInfos);
                log.info("成功同步PRODUCTS_PROPERTY_VALUE表数据到ES，数据量:{}", esInfos.size());
            } catch (Exception e) {
                // 异常捕获后不能往外抛，这里涉及到多个业务的数据处理，抛异常后，其他业务目前没有幂等保障机制
                // 暂时先将错误日志打印并打印相关参数，后续可以将数据发入MQ单独进行这部分的数据的重试处理
                log.error("es同步PRODUCTS_PROPERTY_VALUE表数据异常，操作类型:{}, 同步变更数据:{}",
                    dtsModelEvent.getType(), JSON.toJSONString(esInfos), e);
            }
        }
    }
}
