package net.summerfarm.mall.engine.facade.wms;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.engine.facade.wms.converter.WarehouseSkuInventoryDetailConverter;
import net.summerfarm.mall.engine.facade.wms.dto.WarehouseSkuInventoryDetailDTO;
import net.summerfarm.wms.saleinventory.SaleInventoryProvider;
import net.summerfarm.wms.saleinventory.dto.req.QueryWarehouseSkuInventoryReq;
import net.summerfarm.wms.saleinventory.dto.req.QueryWarehouseSkuInventoryReqDTO;
import net.summerfarm.wms.saleinventory.dto.res.WarehouseSkuInventoryDetailResDTO;
import net.summerfarm.wms.saleinventory.dto.res.WarehouseSkuInventoryResDTO;
import net.summerfarm.wms.saleinventory.dto.res.WarehouseSkuInventoryResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-07-18
 * @description
 */
@Slf4j
@Component
public class SaleInventoryProviderFacade {

    @DubboReference
    private SaleInventoryProvider saleInventoryProvider;

    public List<WarehouseSkuInventoryDetailDTO> queryWarehouseSkuInventoryInfo(QueryWarehouseSkuInventoryReqDTO reqDTO){

        try {
            DubboResponse<WarehouseSkuInventoryResDTO>  dubboResponse =  saleInventoryProvider.queryWarehouseSkuInventoryInfo(reqDTO);
            if (dubboResponse.isSuccess()){
                WarehouseSkuInventoryResDTO resDTO = dubboResponse.getData();
                List<WarehouseSkuInventoryDetailResDTO> detailResDTOS =  resDTO.getWarehouseSkuInventoryDetailResDTOS();
                if (!CollectionUtils.isEmpty(detailResDTOS)){
                    List<WarehouseSkuInventoryDetailDTO> list = detailResDTOS.stream().map(
                            WarehouseSkuInventoryDetailConverter::WarehouseSkuInventoryDetailResp2Dto
                    ).collect(Collectors.toList());
                    return list;
                }
            }else {
                log.error("queryWarehouseSkuInventoryInfo param:{},msg:{},code:{}", JSON.toJSONString(reqDTO),dubboResponse.getMsg(),dubboResponse.getCode());
            }
        } catch (Exception e) {
            log.error("queryWarehouseSkuInventoryInfo error:{}",e);
        }
        return null;
    }
    public List<WarehouseSkuInventoryDetailDTO> queryWarehouseSkuInventory(QueryWarehouseSkuInventoryReq req){
        DubboResponse<WarehouseSkuInventoryResp>   dubboResponse =  saleInventoryProvider.queryWarehouseSkuInventory(req);
        if (dubboResponse.isSuccess()){
            WarehouseSkuInventoryResp resDTO = dubboResponse.getData();
            List<WarehouseSkuInventoryDetailResDTO> detailResDTOS =  resDTO.getWarehouseSkuInventoryDetailResDTOS();
            if (!CollectionUtils.isEmpty(detailResDTOS)){
                List<WarehouseSkuInventoryDetailDTO> list = detailResDTOS.stream().map(
                        WarehouseSkuInventoryDetailConverter::WarehouseSkuInventoryDetailResp2Dto
                ).collect(Collectors.toList());
                return list;
            }
        } else {
            log.error("queryWarehouseSkuInventory param:{},msg:{},code:{}", JSON.toJSONString(req),dubboResponse.getMsg(),dubboResponse.getCode());
        }

        return null;
    }
}
