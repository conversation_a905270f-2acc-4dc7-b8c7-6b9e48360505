package net.summerfarm.mall.engine.facade.wms.converter;

import net.summerfarm.mall.engine.facade.wms.dto.WarehouseSkuInventoryDetailDTO;
import net.summerfarm.wms.saleinventory.dto.res.WarehouseSkuInventoryDetailResDTO;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-07-18
 * @description
 */
public class WarehouseSkuInventoryDetailConverter {

    public static WarehouseSkuInventoryDetailDTO WarehouseSkuInventoryDetailResp2Dto(WarehouseSkuInventoryDetailResDTO resDTO){
        WarehouseSkuInventoryDetailDTO dto = new WarehouseSkuInventoryDetailDTO();
        dto.setTenantId(resDTO.getTenantId());
        dto.setWarehouseNo(resDTO.getWarehouseNo());
        dto.setWarehouseTenantId(resDTO.getWarehouseTenantId());
        dto.setSkuCode(resDTO.getSkuCode());
        dto.setSkuTenantId(resDTO.getSkuTenantId());
        dto.setQuantity(resDTO.getQuantity());
        dto.setOnlineQuantity(resDTO.getOnlineQuantity());
        dto.setAvailableQuantity(resDTO.getAvailableQuantity());
        dto.setReserveMaxQuantity(resDTO.getReserveMaxQuantity());
        dto.setReserveMinQuantity(resDTO.getReserveMinQuantity());
        dto.setReserveUseQuantity(resDTO.getReserveUseQuantity());
        dto.setWarningQuantity(resDTO.getWarningQuantity());
        dto.setSafeQuantity(resDTO.getSafeQuantity());
        dto.setLockQuantity(resDTO.getLockQuantity());
        dto.setSaleLockQuantity(resDTO.getSaleLockQuantity());
        dto.setRoadQuantity(resDTO.getRoadQuantity());
        dto.setCostPrice(resDTO.getCostPrice());
        dto.setMarketPrice(resDTO.getMarketPrice());
        return dto;
    }
}
