package net.summerfarm.mall.engine.common.util;

import net.summerfarm.mall.engine.common.enums.SummerfarmAreaSkuInfoFieldEnum;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Sets;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class EsProductInfoUtil {

    private static final Map<String, Set<String>> tableChangeFieldMap = new HashMap<>();

    public static final String ES_MARKET_PREFIX = "es:item:";

    public static final String ES_MARKET_INDEX = "summerfarm_item_info";

    public static final String ES_AREA_SKU_INDEX = "xianmu_area_sku_info";

    /**
     * 同步ES表表名称
     */
    public static final String MARKET = "market";

    public static final String MARKET_DETAIL = "market_detail";

    public static final String MARKET_ITEM = "market_item";

    public static final String PRODUCT_SKU = "product_sku";

    public static final String MARKET_ITEM_DETAIL = "market_item_detail";

    public static final String MARKET_ITEM_PRICE = "market_item_price";

    public static final String MARKET_ITEM_ONSALE_STRATEGY_MAPPING = "market_item_onsale_strategy_mapping";
    public static final String AREA_STORE = "area_store";

    public static final String WAREHOUSE_INVENTORY_MAPPING = "warehouse_inventory_mapping";

    public static final String PRODUCTS_PROPERTY_VALUE = "products_property_value";

    public static final String INTEREST_RATE_CONFIG = "interest_rate_config";

    public static final String AREA_SKU = "area_sku";

    public static final String PRODUCTS = "products";

    public static final String INVENTORY = "inventory";

    public static final String TIMING_RULE = "timing_rule";

    public static final String MARKET_ITEM_LABEL = "market_item_label";



    public static final Map<String, String> dbFieldToEsField = new HashMap<>(16);

    static {

        tableChangeFieldMap.put(MARKET_ITEM_ONSALE_STRATEGY_MAPPING, Sets.newHashSet("m_type","show_flag","item_id","target_id","on_sale","strategy_type"));

        tableChangeFieldMap.put(MARKET_ITEM, Sets.newHashSet("title","sub_title","main_picture","detail_picture","specification","specification_unit",
        "delete_flag","mini_order_quantity","video_url","after_sale_rule_detail"
        ));
        tableChangeFieldMap.put(MARKET_ITEM_DETAIL, Sets.newHashSet("ext_type","base_sale_unit","average_price_flag","customer_id","sub_type","quote_type"));

        tableChangeFieldMap.put(MARKET, Sets.newHashSet("title","sub_title","category_id","main_picture","delete_flag"));

        tableChangeFieldMap.put(MARKET_DETAIL, Sets.newHashSet("slogan"));

        tableChangeFieldMap.put(PRODUCTS, com.google.common.collect.Sets.newHashSet("quality_time", "quality_time_unit"));

//        tableChangeFieldMap.put(INVENTORY, com.google.common.collect.Sets.newHashSet("weight", "unit", "sku", "sku_name", "sku_pic",
//                "base_sale_unit", "base_sale_quantity", "outdated", "ext_type"));

        tableChangeFieldMap.put(AREA_SKU, com.google.common.collect.Sets.newHashSet( "pd_priority", "sales_mode", "limited_quantity",
                "fix_num", "fix_flag", "price"));
        tableChangeFieldMap.put(INTEREST_RATE_CONFIG, com.google.common.collect.Sets.newHashSet("interest_rate"));

        tableChangeFieldMap.put(AREA_STORE, com.google.common.collect.Sets.newHashSet("reserve_max_quantity", "reserve_min_quantity",
                "reserve_use_quantity", "online_quantity", "area_no", "sku"));

        tableChangeFieldMap.put(WAREHOUSE_INVENTORY_MAPPING, com.google.common.collect.Sets.newHashSet("warehouse_no","store_no"));

        tableChangeFieldMap.put(PRODUCTS_PROPERTY_VALUE, com.google.common.collect.Sets.newHashSet("products_property_value", "sku", "pd_id"));

        tableChangeFieldMap.put(PRODUCT_SKU, com.google.common.collect.Sets.newHashSet("buyer_id","buyer_name","net_weight_num","weight","net_weight_unit"));

        tableChangeFieldMap.put(TIMING_RULE, com.google.common.collect.Sets.newHashSet("display"));

        Arrays.stream(SummerfarmAreaSkuInfoFieldEnum.values())
                .filter(el -> StringUtils.isNotEmpty(el.getDbFieldName()))
                .forEach(el -> dbFieldToEsField.put(el.getDbFieldName(), el.getEsFieldName()));
    }

    /**
     * 更新时-校验并获取需要被放入es中的数据
     *
     * @param tableName  表名
     * @param columnData 数据库字段名及对应的值集合数据 key-列名 value-列值
     * @return 需要被存入es中的字段及对应的字段值 key-列名 value-列值
     */
    public static Boolean binlogToEsData(String tableName, Map<String, String> columnData) {
        Set<String> changeFieldSet = tableChangeFieldMap.get(tableName);
        if (MapUtils.isEmpty(columnData)) {
            return false;
        }

        for (Map.Entry<String, String> entry : columnData.entrySet()) {
            String key = entry.getKey();
            if (changeFieldSet.contains(key)){
                return true;
            }
        }
        return false;
    }
}
